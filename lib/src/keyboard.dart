part of fulde_keyboard;

/// The default keyboard height. Can we overriden by passing
///  `height` argument to `VirtualKeyboard` widget.
const double _virtualKeyboardDefaultHeight = 300;

const int _virtualKeyboardBackspaceEventPeriod = 250;

/// Virtual Keyboard widget.
class FuldeKeyboard extends StatefulWidget {
  /// Keyboard Type: Should be inited in creation time.
  final FuldeKeyboardType type;

  /// Callback for Key press event. Called with pressed `Key` object.
  final Function? onKeyPress;

  /// Virtual keyboard height. Default is 300
  final double height;

  /// Virtual keyboard height. Default is full screen width
  final double? width;

  /// Color for key texts and icons.
  final Color textColor;

  /// Font size for keyboard keys.
  final double fontSize;

  /// the custom layout for multi or single language
  final FuldeKeyboardLayoutKeys? customLayoutKeys;

  /// the text controller go get the output and send the default input
  final TextEditingController? textController;

  /// The builder function will be called for each Key object.
  final Widget Function(BuildContext context, FuldeKeyboardKey key)? builder;

  /// Set to true if you want only to show Caps letters.
  final bool alwaysCaps;

  /// inverse the layout to fix the issues with right to left languages.
  final bool reverseLayout;

  /// used for multi-languages with default layouts, the default is English only
  /// will be ignored if customLayoutKeys is not null
  final List<FuldeKeyboardDefaultLayouts>? defaultLayouts;

  /// Callback to notify the parent about the text direction.
  final ValueChanged<TextDirection> onTextDirectionChanged;

  const FuldeKeyboard({
    Key? key,
    required this.type,
    this.onKeyPress,
    this.builder,
    this.width,
    this.defaultLayouts,
    this.customLayoutKeys,
    this.textController,
    this.reverseLayout = false,
    this.height = _virtualKeyboardDefaultHeight,
    this.textColor = Colors.black,
    this.fontSize = 18,
    this.alwaysCaps = false,
    required this.onTextDirectionChanged,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _FuldeKeyboardState();
  }
}

/// Holds the state for Virtual Keyboard class.
class _FuldeKeyboardState extends State<FuldeKeyboard> {
  late FuldeKeyboardType type;
  Function? onKeyPress;
  late TextEditingController textController;
  // The builder function will be called for each Key object.
  Widget Function(BuildContext context, FuldeKeyboardKey key)? builder;
  late double height;
  double? width;
  late Color textColor;
  late double fontSize;
  late bool alwaysCaps;
  late bool reverseLayout;
  late FuldeKeyboardLayoutKeys customLayoutKeys;
  // Text Style for keys.
  late TextStyle textStyle;

  // True if shift is enabled.
  bool isShiftEnabled = false;

  bool isABCEnabled = false;

  TextDirection textDirection = TextDirection.rtl;

  /// Get semantic label for action keys
  String _getActionKeyLabel(FuldeKeyboardKeyAction? action) {
    switch (action) {
      case FuldeKeyboardKeyAction.backspace:
        return 'Backspace';
      case FuldeKeyboardKeyAction.leftShift:
      case FuldeKeyboardKeyAction.rightShift:
        return 'Shift';
      case FuldeKeyboardKeyAction.space:
        return 'Space';
      case FuldeKeyboardKeyAction.enter:
        return 'Enter';
      default:
        return 'Action key';
    }
  }

  /// Get semantic hint for action keys
  String _getActionKeyHint(FuldeKeyboardKeyAction? action) {
    switch (action) {
      case FuldeKeyboardKeyAction.backspace:
        return 'Delete previous character';
      case FuldeKeyboardKeyAction.leftShift:
      case FuldeKeyboardKeyAction.rightShift:
        return 'Toggle uppercase letters';
      case FuldeKeyboardKeyAction.space:
        return 'Insert space or switch language';
      case FuldeKeyboardKeyAction.enter:
        return 'Insert new line';
      default:
        return 'Perform action';
    }
  }

  void _onKeyPress(FuldeKeyboardKey key) {
    // Add haptic feedback for better user experience
    HapticFeedback.lightImpact();

    double deviceWidth = MediaQuery.of(context).size.width;
    double keyboardHeight = height;

    late double kWidth;
    late double kHeight;

    if (key.coords != null) {
      if (type.toString() == "FuldeKeyboardType.alphanumeric") {
        if (key.coords![1] == 0) {
          kWidth = deviceWidth / customLayoutKeys.newFulbeLayout[0].length;
        } else if (key.coords![1] == 1) {
          kWidth = deviceWidth / customLayoutKeys.newFulbeLayout[1].length;
        } else if (key.coords![1] == 2) {
          kWidth = deviceWidth / customLayoutKeys.newFulbeLayout[2].length;
        } else if (key.coords![1] == 3) {
          kWidth = deviceWidth / customLayoutKeys.newFulbeLayout[3].length;
        }
        kHeight = keyboardHeight / customLayoutKeys.newFulbeLayout.length;
      } else if (type.toString() == "FuldeKeyboardType.alt") {
        if (key.coords![1] == 0) {
          kWidth = deviceWidth / _keyRowsAlt[0].length;
        } else if (key.coords![1] == 1) {
          kWidth = deviceWidth / _keyRowsAlt[1].length;
        } else if (key.coords![1] == 2) {
          kWidth = deviceWidth / _keyRowsAlt[2].length;
        } else if (key.coords![1] == 3) {
          kWidth = deviceWidth / _keyRowsAlt[3].length;
        } else if (key.coords![1] == 4) {
          kWidth = deviceWidth / _keyRowsAlt[4].length;
        }
        kHeight = keyboardHeight / customLayoutKeys.newFulbeLayout.length;
      }
    }

    String keyToDisplay = "";
    if (isABCEnabled) {
      if (isShiftEnabled) {
        keyToDisplay = key.latin != null ? key.latin!.toUpperCase() : '';
      } else {
        keyToDisplay = key.latin ?? '';
      }
    } else {
      switch (customLayoutKeys.activeIndex) {
        case 0: // Fulde
          keyToDisplay = isShiftEnabled ? (key.upper ?? '') : (key.latin ?? '');
          break;
        case 1: // Latin
          keyToDisplay = isShiftEnabled ? "LA1" : (key.fulde ?? '');
          break;
        case 2: // English
          keyToDisplay = "FU1";
          break;
        default:
          keyToDisplay = isShiftEnabled ? (key.upper ?? '') : (key.latin ?? '');
      }
    }

    try {
      currentOverlayEntry?.remove();
    } catch (e) {
      // Ignore overlay removal errors
    }

    OverlayEntry? overlayEntry;
    if (key.keyType == FuldeKeyboardKeyType.string) {
      Widget customWidget = isABCEnabled
          ? Container()
          : Material(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Text(
                  keyToDisplay,
                  style: const TextStyle(
                    fontFamily: 'Fulde',
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            );
      overlayEntry = OverlayEntry(
        builder: (BuildContext context) {
          return Positioned(
            left: key.coords![0].toDouble() * kWidth,
            bottom: keyboardHeight - ((key.coords![1].toDouble()) * kHeight),
            child: customWidget,
          );
        },
      );

      if (customLayoutKeys.activeIndex != 2) {
        Overlay.of(context).insert(overlayEntry);
      }
    }

    if (key.keyType == FuldeKeyboardKeyType.string) {
      textController.text += ((isShiftEnabled ? key.capsText : key.text) ?? '');
    } else if (key.keyType == FuldeKeyboardKeyType.action) {
      switch (key.action) {
        case FuldeKeyboardKeyAction.backspace:
          if (textController.text.isEmpty) return;
          textController.text =
              textController.text.substring(0, textController.text.length - 1);
          break;
        case FuldeKeyboardKeyAction.enter:
          textController.text += '\n';
          break;
        case FuldeKeyboardKeyAction.space:
          textController.text += (key.text ?? '');
          break;
        case FuldeKeyboardKeyAction.leftShift:
        case FuldeKeyboardKeyAction.rightShift:
        case FuldeKeyboardKeyAction.alt:
        default:
          break;
      }
    }

    onKeyPress?.call(key);

    if (key.keyType == FuldeKeyboardKeyType.string) {
      Future.delayed(const Duration(milliseconds: 800), () {
        if (overlayEntry != null && overlayEntry.mounted) {
          overlayEntry.remove();
        }
      });
    }

    currentOverlayEntry = overlayEntry;
  }

  @override
  dispose() {
    if (widget.textController == null) // dispose if created locally only
    {
      textController.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(FuldeKeyboard oldWidget) {
    super.didUpdateWidget(oldWidget);
    setState(() {
      type = widget.type;
      builder = widget.builder;
      onKeyPress = widget.onKeyPress;
      height = widget.height;
      width = widget.width;
      textColor = widget.textColor;
      fontSize = widget.fontSize;
      alwaysCaps = widget.alwaysCaps;
      reverseLayout = widget.reverseLayout;
      textController = widget.textController ?? textController;
      customLayoutKeys = widget.customLayoutKeys ?? customLayoutKeys;
      // Init the Text Style for keys.
      textStyle = TextStyle(
        fontFamily: 'Fulde',
        fontWeight: FontWeight.bold,
        fontSize: fontSize,
        color: textColor,
      );
    });
  }

  @override
  void initState() {
    super.initState();

    textController = widget.textController ?? TextEditingController();
    width = widget.width;
    type = widget.type;
    customLayoutKeys = widget.customLayoutKeys ??
        VirtualKeyboardDefaultLayoutKeys(
            widget.defaultLayouts ?? [FuldeKeyboardDefaultLayouts.english]);
    builder = widget.builder;
    onKeyPress = widget.onKeyPress;
    height = widget.height;
    textColor = widget.textColor;
    fontSize = widget.fontSize;
    alwaysCaps = widget.alwaysCaps;
    reverseLayout = widget.reverseLayout;
    // Init the Text Style for keys.
    textStyle = TextStyle(
      fontFamily: 'Fulde',
      fontWeight: FontWeight.w100,
      fontSize: fontSize,
      color: textColor,
    );
  }

// TextStyle(
//   fontFamily: 'Fulde',
//   fontWeight: FontWeight.bold,
//   fontSize: fontSize,
//   color: textColor,
//   decoration: TextDecoration.none,
//   decorationColor: textColor,
// );

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // height: height,
      width: width ?? MediaQuery.of(context).size.width,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: _rows(),
      ),
    );
  }

  /// Returns the rows for keyboard.
  List<Widget> _rows() {
    // Get the keyboard Rows
    late List<List<FuldeKeyboardKey>> keyboardRows;

    if (type == FuldeKeyboardType.numeric) {
      keyboardRows = _getKeyboardRowsNumeric();
    } else if (type == FuldeKeyboardType.alt) {
      keyboardRows = _getKeyboardRowsAlt();
    } else {
      keyboardRows = _getKeyboardRowsAlphaNumeric(customLayoutKeys);
    }

    // Generate keyboard row.
    List<Widget> rows = List.generate(keyboardRows.length, (int rowNum) {
      var items = List.generate(keyboardRows[rowNum].length, (int keyNum) {
        // Get the VirtualKeyboardKey object.
        FuldeKeyboardKey virtualKeyboardKey = keyboardRows[rowNum][keyNum];

        Widget keyWidget;

        // Check if builder is specified.
        // Call builder function if specified or use default
        //  Key widgets if not.
        if (builder == null) {
          // Check the key type.
          switch (virtualKeyboardKey.keyType) {
            case FuldeKeyboardKeyType.string:
              // Draw String key.
              keyWidget = _keyboardDefaultKey(virtualKeyboardKey);
              break;
            case FuldeKeyboardKeyType.action:
              // Draw action key.
              keyWidget = _keyboardDefaultActionKey(virtualKeyboardKey);
              break;
          }
        } else {
          // Call the builder function, so the user can specify custom UI for keys.
          keyWidget = builder!(context, virtualKeyboardKey);

          // if (keyWidget == null) {
          //   throw 'builder function must return Widget';
          // }
        }

        return keyWidget;
      });

      if (reverseLayout) items = items.reversed.toList();
      return Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A1A),
              Color(0xFF0F0F0F),
            ],
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              // Generate keyboard keys
              children: items,
            ),
            // ?* Add lines to each row
            // const Divider(
            //   color: Colors.white38,
            //   height: 0,
            // )
          ],
        ),
      );
    });

    return rows;
  }

  // True if long press is enabled.
  bool longPress = false;

  /// Creates default UI element for keyboard Key.
  Widget _keyboardDefaultKey(FuldeKeyboardKey key) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 2.0, vertical: 3.0),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          child: Semantics(
            button: true,
            label: 'Key ${key.text ?? key.capsText ?? ''}',
            hint: 'Tap to type ${key.text ?? key.capsText ?? ''}',
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              splashColor: Colors.white.withValues(alpha: 0.1),
              highlightColor: Colors.white.withValues(alpha: 0.05),
              onLongPress: () {
                _onLongKeyPress(key);
              },
              onTap: () {
                _onKeyPress(key);
              },
              onTapDown: (_) {
                // Add scale animation on press
                setState(() {});
              },
              onTapUp: (_) {
                // Reset scale animation
                setState(() {});
              },
              onTapCancel: () {
                // Reset scale animation on cancel
                setState(() {});
              },
              child: Container(
                height: (height / customLayoutKeys.activeLayout.length) - 6,
                margin: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF3A3A3A),
                      Color(0xFF2A2A2A),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF4A4A4A),
                    width: 0.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.05),
                      offset: const Offset(0, -1),
                      blurRadius: 1,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    alwaysCaps
                        ? key.capsText ?? ''
                        : (isShiftEnabled ? key.capsText : key.text) ?? '',
                    style: textStyle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      shadows: [
                        Shadow(
                          color: Colors.black.withValues(alpha: 0.5),
                          offset: const Offset(0, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  OverlayEntry? currentOverlayEntry;

  void _onLongKeyPress(FuldeKeyboardKey key) {
    /*print("LONG PRESSED");
    print("key.text: ${key.text}");
    print("${key.coords![0]} ${key.coords![1]}");
    print(type.toString());
    print("isABCEnabled: $isABCEnabled");*/

    //6 1
    //7 1
    //8 1
    //0 2

    String keyToDisplay = "";
    //assign respective properties to keys
    if (key.coords != null) {
      if (type.toString() == "FuldeKeyboardType.alphanumeric") {
        if ((key.coords![0] == 2) && (key.coords![1] == 1)) {
          // 1
          //2 1
          keyToDisplay = "\u069B";
        } else if ((key.coords![0] == 6) && (key.coords![1] == 1)) {
          // 2
          //6 1
          keyToDisplay = "\u069D";
        } else if ((key.coords![0] == 7) && (key.coords![1] == 1)) {
          //3
          //7 1
          keyToDisplay = "\u069C";
        } else if ((key.coords![0] == 8) && (key.coords![1] == 1)) {
          //4
          //8 1
          keyToDisplay = "\u069E";
        } else if ((key.coords![0] == 0) && (key.coords![1] == 2)) {
          //5
          //0 2
          keyToDisplay = "\u069A";
        } else if ((key.coords![0] == 2) && (key.coords![1] == 4)) {
          //5
          //2 4
          keyToDisplay = "24";
        }
      }
    }
    debugPrint(keyToDisplay.toString());

    /*String keyToDisplay = "";
    if (isABCEnabled) {
      //english
      if (isShiftEnabled) {
        keyToDisplay = key.latin != null ? key.latin!.toUpperCase() : '';
      } else {
        keyToDisplay = key.latin!;
      }
    } else {
      //fulbe
      if (isShiftEnabled) {
        keyToDisplay = key.upper ?? ''; //character map for fulbe UPPERCASE
      } else {
        keyToDisplay = key.latin ?? '';
      }
    }*/

    double deviceWidth = MediaQuery.of(context).size.width;

    double keyboardHeight = height;

    // height and width specifications
    late double kWidth;
    late double kHeight; // = 60;

    //divide screenwidth by number of keys: kWidth
    if (key.coords != null) {
      if (type.toString() == "FuldeKeyboardType.alphanumeric") {
        if (key.coords![1] == 0) {
          //row1
          kWidth = deviceWidth / customLayoutKeys.newFulbeLayout[0].length;
        } else if (key.coords![1] == 1) {
          //row2
          kWidth = deviceWidth / customLayoutKeys.newFulbeLayout[1].length;
        } else if (key.coords![1] == 2) {
          //row3
          kWidth = deviceWidth / customLayoutKeys.newFulbeLayout[2].length;
        } else if (key.coords![1] == 3) {
          //row4
          kWidth = deviceWidth / customLayoutKeys.newFulbeLayout[3].length;
        }
        kHeight = keyboardHeight / customLayoutKeys.newFulbeLayout.length;
        // else { //row5
        //   kWidth = 36; //*default
        // }
      } else if (type.toString() == "FuldeKeyboardType.alt") {
        if (key.coords![1] == 0) {
          //row1
          kWidth = deviceWidth / _keyRowsAlt[0].length;
        } else if (key.coords![1] == 1) {
          //row2
          kWidth = deviceWidth / _keyRowsAlt[1].length;
        } else if (key.coords![1] == 2) {
          //row3
          kWidth = deviceWidth / _keyRowsAlt[2].length;
        } else if (key.coords![1] == 3) {
          //row4
          kWidth = deviceWidth / _keyRowsAlt[3].length;
        } else if (key.coords![1] == 4) {
          //row5
          kWidth = deviceWidth / _keyRowsAlt[4].length;
        }
        kHeight = keyboardHeight / customLayoutKeys.newFulbeLayout.length;
        // else { //row5
        //   kWidth = 36; //*default
        // }
      }
    }

    // Close the previously opened overlay
    //currentOverlayEntry?.remove();
    try {
      currentOverlayEntry?.remove();
    } catch (e) {
      // Handle any exception that may occur
      //print('Error occurred while removing overlay entry: $e');
    }

    OverlayEntry? overlayEntry;

    Widget customWidget = isABCEnabled
        ? Container()
        : Material(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.blueGrey.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(4),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              child: GestureDetector(
                  onTap: () {
                    onKeyPress?.call(key); //<- not in use
                    textController.text +=
                        ((isShiftEnabled ? keyToDisplay : keyToDisplay));
                    overlayEntry?.remove();
                  },
                  child: Row(
                    children: <Widget>[
                      Text(
                        keyToDisplay,
                        style: const TextStyle(
                          fontFamily: 'Fulde',
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  )),
            ),
          );

    overlayEntry = OverlayEntry(
      builder: (BuildContext context) {
        return Positioned(
          left: key.coords![0].toDouble() * kWidth,
          bottom: keyboardHeight - ((key.coords![1].toDouble()) * kHeight),
          child: customWidget,
        );
      },
    );

    //display the pop over
    if (keyToDisplay.isNotEmpty) {
      Overlay.of(context).insert(overlayEntry);
    }

    (keyToDisplay != "") ? Overlay.of(context).insert(overlayEntry) : null;

    currentOverlayEntry = overlayEntry;

    /*if (key.keyType == FuldeKeyboardKeyType.string) {
      Future.delayed(const Duration(milliseconds: 800), () {
        overlayEntry!.remove();
      });
    }*/
  }

  /// Creates default UI element for keyboard Action Key.
  Widget _keyboardDefaultActionKey(FuldeKeyboardKey key) {
    Widget? actionKey;

    switch (key.action ?? FuldeKeyboardKeyAction.switchLanguage) {
      case FuldeKeyboardKeyAction.backspace:
        actionKey = GestureDetector(
            onLongPress: () {
              longPress = true;
              Timer.periodic(
                  const Duration(
                      milliseconds: _virtualKeyboardBackspaceEventPeriod),
                  (timer) {
                if (longPress) {
                  _onKeyPress(key);
                } else {
                  timer.cancel();
                }
              });
            },
            onLongPressUp: () {
              longPress = false;
            },
            child: SizedBox(
              height: double.infinity,
              width: double.infinity,
              child: Icon(
                CupertinoIcons.delete_left_fill,
                color: textColor,
              ),
            ));
        break;
      case FuldeKeyboardKeyAction.leftShift:
      case FuldeKeyboardKeyAction.rightShift:
        actionKey = SizedBox(
          height: double.infinity,
          width: double.infinity,
          child: Center(
            child: Text(
              '\u21E7',
              style: textStyle,
            ),
          ),
        );
        break;
      case FuldeKeyboardKeyAction.space:
        actionKey = SizedBox(
          height: double.infinity,
          width: double.infinity,
          child: Center(
            child: Text(
              customLayoutKeys.activeIndex == 0
                  ? '\u06A9\u069F\u06BC\u06A2'
                  : customLayoutKeys.activeIndex == 1
                      ? 'Latin'
                      : 'English',
              style: textStyle,
            ),
          ),
        );
        break;
      case FuldeKeyboardKeyAction.enter:
        actionKey = Icon(
          Icons.keyboard_return,
          color: textColor,
        );
        break;
      case FuldeKeyboardKeyAction.alt:
        actionKey = GestureDetector(
            onTap: () {
              setState(() {
                type = FuldeKeyboardType.alt;
              });
            },
            child: SizedBox(
              height: double.infinity,
              width: double.infinity,
              child: Center(
                child: Text(
                  customLayoutKeys.activeIndex == 0 ? '\u2387' : '!@#',
                  style: textStyle,
                ),
              ),
            ));
        break;
      case FuldeKeyboardKeyAction.switchLanguage:
        actionKey = GestureDetector(
            onTap: () {},
            child: const SizedBox(
              height: double.infinity,
              width: double.infinity,
            ));
        break;
      case FuldeKeyboardKeyAction.switchAbc:
        actionKey = GestureDetector(
            onTap: () {
              setState(() {
                type = FuldeKeyboardType.alphanumeric;
              });
            },
            child: SizedBox(
              height: double.infinity,
              width: double.infinity,
              child: Center(
                child: Text(
                  'ABC',
                  style: TextStyle(color: textColor),
                ),
              ),
            ));
        break;
      case FuldeKeyboardKeyAction.switchNumberPad:
        actionKey = GestureDetector(
            onTap: () {
              setState(() {
                type = FuldeKeyboardType.numeric;
              });
            },
            child: SizedBox(
              height: double.infinity,
              width: double.infinity,
              child: Center(
                child: Text(
                  customLayoutKeys.activeIndex == 0
                      ? '\u0661\u0662\u0663'
                      : '123',
                  style: textStyle,
                ),
              ),
            ));
        break;
    }

    var widget = InkWell(
      onTap: () {
        if (key.action == FuldeKeyboardKeyAction.leftShift ||
            key.action == FuldeKeyboardKeyAction.rightShift) {
          if (!alwaysCaps) {
            setState(() {
              isShiftEnabled = !isShiftEnabled;
            });
          }
        }
        _onKeyPress(key);
      },
      onLongPress: () {
        OverlayEntry? overlayEntry;

        Widget customWidget = isABCEnabled
            ? Container()
            : Material(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.of(context).size.width * 0.9,
                    minWidth: MediaQuery.of(context).size.width * 0.7,
                  ),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFF2A2A2A),
                        Color(0xFF1A1A1A),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.5),
                        offset: const Offset(0, 4),
                        blurRadius: 8,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      Flexible(
                        child: GestureDetector(
                          onTap: () {
                            textDirection = TextDirection.rtl;
                            changeTextDirection(textDirection);
                            setState(() {
                              customLayoutKeys.switchLanguage(0);
                            });
                            overlayEntry?.remove();
                          },
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            curve: Curves.easeInOut,
                            margin: const EdgeInsets.symmetric(
                                horizontal: 4, vertical: 4),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              gradient: customLayoutKeys.activeIndex == 0
                                  ? const LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Color(0xFF4A90E2),
                                        Color(0xFF357ABD),
                                      ],
                                    )
                                  : null,
                              color: customLayoutKeys.activeIndex == 0
                                  ? null
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: customLayoutKeys.activeIndex == 0
                                    ? Colors.transparent
                                    : Colors.grey.withValues(alpha: 0.3),
                                width: 1,
                              ),
                              boxShadow: customLayoutKeys.activeIndex == 0
                                  ? [
                                      BoxShadow(
                                        color: const Color(0xFF4A90E2)
                                            .withValues(alpha: 0.3),
                                        offset: const Offset(0, 2),
                                        blurRadius: 4,
                                        spreadRadius: 0,
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Center(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (customLayoutKeys.activeIndex == 0)
                                    const Padding(
                                      padding: EdgeInsets.only(right: 6.0),
                                      child: Icon(
                                        Icons.check_circle,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                    ),
                                  Text(
                                    'Fulde',
                                    style: TextStyle(
                                      color: customLayoutKeys.activeIndex == 0
                                          ? Colors.white
                                          : Colors.grey.withValues(alpha: 0.9),
                                      fontWeight:
                                          customLayoutKeys.activeIndex == 0
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                      fontSize: 16,
                                      letterSpacing: 0.5,
                                      shadows: customLayoutKeys.activeIndex == 0
                                          ? [
                                              const Shadow(
                                                color: Colors.black26,
                                                offset: Offset(0, 1),
                                                blurRadius: 2,
                                              ),
                                            ]
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: GestureDetector(
                          onTap: () {
                            textDirection = TextDirection.ltr;
                            changeTextDirection(textDirection);
                            setState(() {
                              customLayoutKeys.switchLanguage(1);
                            });
                            overlayEntry?.remove();
                          },
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            curve: Curves.easeInOut,
                            margin: const EdgeInsets.symmetric(
                                horizontal: 4, vertical: 4),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              gradient: customLayoutKeys.activeIndex == 1
                                  ? const LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Color(0xFF4A90E2),
                                        Color(0xFF357ABD),
                                      ],
                                    )
                                  : null,
                              color: customLayoutKeys.activeIndex == 1
                                  ? null
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: customLayoutKeys.activeIndex == 1
                                    ? Colors.transparent
                                    : Colors.grey.withValues(alpha: 0.3),
                                width: 1,
                              ),
                              boxShadow: customLayoutKeys.activeIndex == 1
                                  ? [
                                      BoxShadow(
                                        color: const Color(0xFF4A90E2)
                                            .withValues(alpha: 0.3),
                                        offset: const Offset(0, 2),
                                        blurRadius: 4,
                                        spreadRadius: 0,
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Center(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (customLayoutKeys.activeIndex == 1)
                                    const Padding(
                                      padding: EdgeInsets.only(right: 6.0),
                                      child: Icon(
                                        Icons.check_circle,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                    ),
                                  Text(
                                    'Latin',
                                    style: TextStyle(
                                      color: customLayoutKeys.activeIndex == 1
                                          ? Colors.white
                                          : Colors.grey.withValues(alpha: 0.9),
                                      fontWeight:
                                          customLayoutKeys.activeIndex == 1
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                      fontSize: 16,
                                      letterSpacing: 0.5,
                                      shadows: customLayoutKeys.activeIndex == 1
                                          ? [
                                              const Shadow(
                                                color: Colors.black26,
                                                offset: Offset(0, 1),
                                                blurRadius: 2,
                                              ),
                                            ]
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: GestureDetector(
                          onTap: () {
                            textDirection = TextDirection.ltr;
                            changeTextDirection(textDirection);
                            setState(() {
                              customLayoutKeys.switchLanguage(2);
                            });
                            overlayEntry?.remove();
                          },
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            curve: Curves.easeInOut,
                            margin: const EdgeInsets.symmetric(
                                horizontal: 4, vertical: 4),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              gradient: customLayoutKeys.activeIndex == 2
                                  ? const LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: [
                                        Color(0xFF4A90E2),
                                        Color(0xFF357ABD),
                                      ],
                                    )
                                  : null,
                              color: customLayoutKeys.activeIndex == 2
                                  ? null
                                  : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: customLayoutKeys.activeIndex == 2
                                    ? Colors.transparent
                                    : Colors.grey.withValues(alpha: 0.3),
                                width: 1,
                              ),
                              boxShadow: customLayoutKeys.activeIndex == 2
                                  ? [
                                      BoxShadow(
                                        color: const Color(0xFF4A90E2)
                                            .withValues(alpha: 0.3),
                                        offset: const Offset(0, 2),
                                        blurRadius: 4,
                                        spreadRadius: 0,
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Center(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (customLayoutKeys.activeIndex == 2)
                                    const Padding(
                                      padding: EdgeInsets.only(right: 6.0),
                                      child: Icon(
                                        Icons.check_circle,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                    ),
                                  Text(
                                    'English',
                                    style: TextStyle(
                                      color: customLayoutKeys.activeIndex == 2
                                          ? Colors.white
                                          : Colors.grey.withValues(alpha: 0.9),
                                      fontWeight:
                                          customLayoutKeys.activeIndex == 2
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                      fontSize: 16,
                                      letterSpacing: 0.5,
                                      shadows: customLayoutKeys.activeIndex == 2
                                          ? [
                                              const Shadow(
                                                color: Colors.black26,
                                                offset: Offset(0, 1),
                                                blurRadius: 2,
                                              ),
                                            ]
                                          : null,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );

        overlayEntry = OverlayEntry(
          builder: (BuildContext context) {
            Size size = MediaQuery.of(context).size;
            return Positioned(
              left: size.width / 4,
              right: size.width / 4,
              bottom: height / 2,
              child: customWidget,
            );
          },
        );

        Overlay.of(context).insert(overlayEntry);
        currentOverlayEntry = overlayEntry;
      },
      child: Semantics(
        button: true,
        label: _getActionKeyLabel(key.action),
        hint: _getActionKeyHint(key.action),
        child: Container(
          margin: const EdgeInsets.all(3),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF4A4A4A),
                Color(0xFF3A3A3A),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF5A5A5A),
              width: 0.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.4),
                offset: const Offset(0, 2),
                blurRadius: 4,
                spreadRadius: 0,
              ),
              BoxShadow(
                color: Colors.white.withValues(alpha: 0.08),
                offset: const Offset(0, -1),
                blurRadius: 1,
                spreadRadius: 0,
              ),
            ],
          ),
          alignment: Alignment.center,
          child: SizedBox(
            height: height / customLayoutKeys.activeLayout.length,
            child: actionKey,
          ),
        ),
      ),
    );

    if (key.action == FuldeKeyboardKeyAction.space) {
      return SizedBox(
          width: (width ?? MediaQuery.of(context).size.width) / 2,
          child: widget);
    } else {
      return Expanded(child: widget);
    }
  }

  void changeTextDirection(TextDirection textDirection) {
    widget.onTextDirectionChanged(textDirection);
  }
}
