name: fulde_keyboard
description: A simple package for displaying virtual fulde keyboards. The library is written in Dart and has no native code dependency.
version: 0.9.9

homepage: https://github.com/al-mohad/fulde-keyboard

screenshots:
  - description: "first screenshot description here"
    path: example/screenshots/fuldeLayout.png
  - description: "secod screenshot description here"
    path: example/screenshots/englishLayout.png
  - description: "secod screenshot description here"
    path: example/screenshots/englishLayout2.png
  - description: "secod screenshot description here"
    path: example/screenshots/englishLayout3.png
  - description: "secod screenshot description here"
    path: example/screenshots/englishLayout4.png

environment:
  sdk: ">=2.18.6 <4.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Fulde
      fonts:
        - asset: fonts/fulde.ttf
        # - asset: fonts/fulde.ttf
        #   style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
